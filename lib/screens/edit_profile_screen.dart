import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../services/auth_service.dart';
import 'login_screen.dart';

class EditProfileScreen extends StatefulWidget {
  final VoidCallback? onProfileUpdated;

  const EditProfileScreen({
    super.key,
    this.onProfileUpdated,
  });

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _dobController = TextEditingController();
  final _cityController = TextEditingController();

  final ImagePicker _picker = ImagePicker();
  XFile? _selectedImage;
  String? _currentProfileImageUrl;

  bool _isLoading = false;
  bool _isLoadingData = true;
  bool _isLocationLoading = false;
  bool _isDeletingAccount = false;

  // User data that cannot be changed
  String? _displayName;
  String? _email;
  String? _phoneNumber;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _dobController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    try {
      final user = AuthService.currentUser;
      if (user == null) return;

      final userData = await AuthService.getUserData(user.uid);

      if (userData != null && mounted) {
        setState(() {
          _displayName = userData['displayName'] ?? user.displayName;
          _email = userData['email'] ?? user.email;
          _phoneNumber = userData['phoneNumber'];
          _currentProfileImageUrl = userData['profileImageUrl'];
          _dobController.text = userData['dateOfBirth'] ?? '';
          _cityController.text = userData['city'] ?? '';
          _isLoadingData = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoadingData = false);
        _showErrorSnackBar('Failed to load user data: ${e.toString()}');
      }
    }
  }

  Future<void> _pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to pick image: ${e.toString()}');
    }
  }

  Future<void> _takePhoto() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = image;
        });
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: ${e.toString()}');
    }
  }

  Future<String?> _uploadProfileImage() async {
    if (_selectedImage == null) return _currentProfileImageUrl;

    try {
      final user = AuthService.currentUser;
      if (user == null) throw 'User not authenticated';

      // Create a reference to Firebase Storage
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('profile_images')
          .child('${user.uid}.jpg');

      // Upload the file
      final uploadTask = storageRef.putFile(File(_selectedImage!.path));
      final snapshot = await uploadTask;

      // Get the download URL
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } catch (e) {
      throw 'Failed to upload profile image: $e';
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final user = AuthService.currentUser;
      if (user == null) throw 'User not authenticated';

      // Upload profile image if selected
      final profileImageUrl = await _uploadProfileImage();

      // Prepare update data
      final updateData = <String, dynamic>{};

      if (_dobController.text.trim().isNotEmpty) {
        updateData['dateOfBirth'] = _dobController.text.trim();
      }

      if (_cityController.text.trim().isNotEmpty) {
        updateData['city'] = _cityController.text.trim();
      }

      if (profileImageUrl != null) {
        updateData['profileImageUrl'] = profileImageUrl;
      }

      // Update user data using AuthService
      await AuthService.updateUserData(user.uid, updateData);

      if (mounted) {
        Navigator.of(context).pop();

        // Call the callback to notify parent widget
        widget.onProfileUpdated?.call();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: Color(0xFF3AD29F),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('Failed to update profile: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE74C3C),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 6570)), // 18 years ago
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF3AD29F),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF2D5A5A),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _dobController.text = '${picked.day}/${picked.month}/${picked.year}';
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() => _isLocationLoading = true);

    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        _showErrorSnackBar('Location services are disabled. Please enable them in settings.');
        return;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          _showErrorSnackBar('Location permissions are denied.');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showErrorSnackBar('Location permissions are permanently denied. Please enable them in settings.');
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      // Get address from coordinates
      List<Placemark> placemarks = await placemarkFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        String city = place.locality ?? place.administrativeArea ?? place.subAdministrativeArea ?? '';

        if (city.isNotEmpty) {
          setState(() {
            _cityController.text = city;
          });
        } else {
          _showErrorSnackBar('Could not determine city from your location.');
        }
      } else {
        _showErrorSnackBar('Could not get location details.');
      }
    } catch (e) {
      _showErrorSnackBar('Failed to get location: ${e.toString()}');
    } finally {
      setState(() => _isLocationLoading = false);
    }
  }

  Future<void> _showDeleteAccountDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          title: const Row(
            children: [
              Icon(Icons.warning, color: Color(0xFFE74C3C), size: 28),
              SizedBox(width: 12),
              Text(
                'Delete Account',
                style: TextStyle(
                  color: Color(0xFFE74C3C),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Are you sure you want to delete your account?',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF2D5A5A),
                ),
              ),
              SizedBox(height: 12),
              Text(
                'This action will permanently delete:',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF5A8A8A),
                ),
              ),
              SizedBox(height: 8),
              Text(
                '• All your profile data\n• All your listed items\n• All your account details\n• All uploaded images',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF5A8A8A),
                  height: 1.5,
                ),
              ),
              SizedBox(height: 16),
              Text(
                'This action cannot be undone.',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFFE74C3C),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text(
                'Cancel',
                style: TextStyle(
                  color: Color(0xFF5A8A8A),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteAccount();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFE74C3C),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Delete Account',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deleteAccount() async {
    setState(() => _isDeletingAccount = true);

    try {
      final user = AuthService.currentUser;
      if (user == null) {
        _showErrorSnackBar('No user found to delete.');
        return;
      }

      // Show loading dialog
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Color(0xFFE74C3C)),
                SizedBox(height: 16),
                Text(
                  'Deleting your account...',
                  style: TextStyle(
                    fontSize: 16,
                    color: Color(0xFF2D5A5A),
                  ),
                ),
              ],
            ),
          ),
        );
      }

      // Step 1: Delete user data from Firestore
      await _deleteUserDataFromFirestore(user.uid);

      // Step 2: Delete user files from Storage
      await _deleteUserFilesFromStorage(user.uid);

      // Step 3: Delete user from Authentication (this must be last)
      await user.delete();

      // Navigate to login screen
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        _showErrorSnackBar('Failed to delete account: ${e.toString()}');
      }
    } finally {
      if (mounted) {
        setState(() => _isDeletingAccount = false);
      }
    }
  }

  Future<void> _deleteUserDataFromFirestore(String uid) async {
    final firestore = FirebaseFirestore.instance;
    final batch = firestore.batch();

    try {
      // Delete user document
      final userDoc = firestore.collection('users').doc(uid);
      batch.delete(userDoc);

      // Delete user's items
      final itemsQuery = await firestore
          .collection('items')
          .where('userId', isEqualTo: uid)
          .get();

      for (var doc in itemsQuery.docs) {
        batch.delete(doc.reference);
      }

      // Delete any other user-related collections
      // Add more collections here if needed in the future

      // Commit the batch
      await batch.commit();
    } catch (e) {
      throw 'Failed to delete user data from Firestore: $e';
    }
  }

  Future<void> _deleteUserFilesFromStorage(String uid) async {
    try {
      final storage = FirebaseStorage.instance;

      // Delete profile images
      try {
        await storage.ref().child('profile_images/$uid.jpg').delete();
      } catch (e) {
        // File might not exist, continue
      }

      // Delete item images
      try {
        final itemImagesRef = storage.ref().child('item_images/$uid');
        final listResult = await itemImagesRef.listAll();

        for (var item in listResult.items) {
          await item.delete();
        }
      } catch (e) {
        // Directory might not exist, continue
      }

      // Delete document images
      try {
        await storage.ref().child('documents/$uid.jpg').delete();
      } catch (e) {
        // File might not exist, continue
      }
    } catch (e) {
      throw 'Failed to delete user files from Storage: $e';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoadingData) {
      return Scaffold(
        backgroundColor: const Color(0xFFF5F1E8),
        appBar: AppBar(
          backgroundColor: const Color(0xFFF5F1E8),
          elevation: 0,
          leading: IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.arrow_back,
              color: Color(0xFF2D5A5A),
            ),
          ),
          title: const Text(
            'Edit Profile',
            style: TextStyle(
              color: Color(0xFF2D5A5A),
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ),
        body: const Center(
          child: CircularProgressIndicator(
            color: Color(0xFF3AD29F),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: const Color(0xFFF5F1E8),
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5F1E8),
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(
            Icons.arrow_back,
            color: Color(0xFF2D5A5A),
          ),
        ),
        title: const Text(
          'Edit Profile',
          style: TextStyle(
            color: Color(0xFF2D5A5A),
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProfile,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Color(0xFF3AD29F),
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: Color(0xFF3AD29F),
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Profile Picture Section
              _buildProfilePictureSection(),
              const SizedBox(height: 32),

              // Non-editable fields
              _buildNonEditableSection(),
              const SizedBox(height: 32),

              // Editable fields
              _buildEditableSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfilePictureSection() {
    return Center(
      child: Column(
        children: [
          const Text(
            'Profile Picture',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D5A5A),
            ),
          ),
          const SizedBox(height: 16),

          Stack(
            children: [
              CircleAvatar(
                radius: 60,
                backgroundColor: const Color(0xFF2D5A5A),
                backgroundImage: _selectedImage != null
                    ? FileImage(File(_selectedImage!.path))
                    : (_currentProfileImageUrl != null
                        ? NetworkImage(_currentProfileImageUrl!)
                        : null),
                child: (_selectedImage == null && _currentProfileImageUrl == null)
                    ? const Icon(
                        Icons.person,
                        size: 60,
                        color: Colors.white,
                      )
                    : null,
              ),
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  decoration: const BoxDecoration(
                    color: Color(0xFF3AD29F),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _showImagePickerOptions,
                    icon: const Icon(
                      Icons.camera_alt,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),
          const Text(
            'Tap the camera icon to change your profile picture',
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF5A8A8A),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Select Profile Picture',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D5A5A),
              ),
            ),
            const SizedBox(height: 24),

            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _pickImage();
                    },
                    icon: const Icon(Icons.photo_library, color: Colors.white),
                    label: const Text(
                      'Gallery',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF5A8A8A),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _takePhoto();
                    },
                    icon: const Icon(Icons.camera_alt, color: Colors.white),
                    label: const Text(
                      'Camera',
                      style: TextStyle(color: Colors.white),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF3AD29F),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildNonEditableSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Account Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        const Text(
          'These fields cannot be changed',
          style: TextStyle(
            fontSize: 14,
            color: Color(0xFF5A8A8A),
          ),
        ),
        const SizedBox(height: 16),

        _buildReadOnlyField(
          label: 'Full Name',
          value: _displayName ?? 'Not set',
          icon: Icons.person,
        ),

        const SizedBox(height: 16),

        _buildReadOnlyField(
          label: 'Email Address',
          value: _email ?? 'Not set',
          icon: Icons.email,
        ),

        const SizedBox(height: 16),

        _buildReadOnlyField(
          label: 'Phone Number',
          value: _phoneNumber ?? 'Not verified',
          icon: Icons.phone,
        ),
      ],
    );
  }

  Widget _buildReadOnlyField({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE0E0E0)),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: const Color(0xFF5A8A8A),
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF5A8A8A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Color(0xFF2D5A5A),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Personal Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 16),

        // Date of Birth
        const Text(
          'Date of Birth',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _dobController,
          readOnly: true,
          onTap: _selectDate,
          style: const TextStyle(
            color: Color(0xFF2D5A5A),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: 'Select your date of birth',
            hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
            suffixIcon: const Icon(
              Icons.calendar_today,
              color: Color(0xFF5A8A8A),
            ),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
        ),

        const SizedBox(height: 24),

        // City
        const Text(
          'City',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF2D5A5A),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _cityController,
          style: const TextStyle(
            color: Color(0xFF2D5A5A),
            fontSize: 16,
          ),
          decoration: InputDecoration(
            hintText: 'Enter your city',
            hintStyle: const TextStyle(color: Color(0xFF5A8A8A)),
            suffixIcon: _isLocationLoading
                ? const Padding(
                    padding: EdgeInsets.all(12),
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Color(0xFF3AD29F),
                      ),
                    ),
                  )
                : IconButton(
                    onPressed: _getCurrentLocation,
                    icon: const Icon(
                      Icons.my_location,
                      color: Color(0xFF3AD29F),
                    ),
                    tooltip: 'Use current location',
                  ),
            filled: true,
            fillColor: Colors.white,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFFE0E0E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: const BorderSide(color: Color(0xFF3AD29F), width: 2),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
          validator: (value) {
            if (value?.trim().isNotEmpty == true && value!.trim().length < 2) {
              return 'City name should be at least 2 characters';
            }
            return null;
          },
        ),

        const SizedBox(height: 40),

        // Delete Account Section
        _buildDeleteAccountSection(),

        const SizedBox(height: 40),
      ],
    );
  }

  Widget _buildDeleteAccountSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Danger Zone',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Color(0xFFE74C3C),
          ),
        ),
        const SizedBox(height: 16),

        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: const Color(0xFFE74C3C).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFFE74C3C).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(
                    Icons.warning_outlined,
                    color: Color(0xFFE74C3C),
                    size: 24,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'Delete Account',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFFE74C3C),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const Text(
                'Permanently delete your account and all associated data. This action cannot be undone.',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF5A8A8A),
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isDeletingAccount ? null : _showDeleteAccountDialog,
                  icon: _isDeletingAccount
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                      : const Icon(Icons.delete_forever, color: Colors.white),
                  label: Text(
                    _isDeletingAccount ? 'Deleting...' : 'Delete Account',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFE74C3C),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}