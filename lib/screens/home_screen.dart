import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../services/auth_service.dart';
import 'login_screen.dart';
import 'list_item_screen.dart';

class HomeScreen extends StatefulWidget {
  final bool showWelcomeMessage;

  const HomeScreen({
    super.key,
    this.showWelcomeMessage = false,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  bool _showWelcome = false;
  bool _isFirstTimeUser = false;

  @override
  void initState() {
    super.initState();
    if (widget.showWelcomeMessage) {
      _initializeWelcomeMessage();
    }
  }

  Future<void> _initializeWelcomeMessage() async {
    // Check if user is first time by looking at Firestore creation date
    final user = AuthService.currentUser;
    if (user?.email != null) {
      try {
        final userDoc = await FirebaseFirestore.instance
            .collection('users')
            .doc(user!.email)
            .get();

        if (userDoc.exists) {
          final data = userDoc.data();
          final createdAt = data?['createdAt'] as Timestamp?;
          final lastSignIn = data?['lastSignIn'] as Timestamp?;

          // Consider user as first time if account was created in the last 5 minutes
          if (createdAt != null && lastSignIn != null) {
            final now = DateTime.now();
            final createdTime = createdAt.toDate();
            final timeDifference = now.difference(createdTime).inMinutes;

            setState(() {
              _isFirstTimeUser = timeDifference <= 5;
              _showWelcome = true;
            });

            // Hide welcome message after 4 seconds
            Future.delayed(const Duration(seconds: 4), () {
              if (mounted) {
                setState(() {
                  _showWelcome = false;
                });
              }
            });
          }
        }
      } catch (e) {
        // If there's an error, default to showing welcome back
        setState(() {
          _isFirstTimeUser = false;
          _showWelcome = true;
        });

        Future.delayed(const Duration(seconds: 4), () {
          if (mounted) {
            setState(() {
              _showWelcome = false;
            });
          }
        });
      }
    }
  }

  Future<void> _handleSignOut(BuildContext context) async {
    try {
      await AuthService.signOut();
      if (context.mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => const LoginScreen()),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString()),
            backgroundColor: const Color(0xFFE74C3C),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F1E8), // Same cream background
      appBar: AppBar(
        backgroundColor: const Color(0xFFF5F1E8),
        elevation: 0,
        title: const Text(
          'EvenOut',
          style: TextStyle(
            color: Color(0xFF2D5A5A),
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
      ),
      body: _buildCurrentPage(),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: const Color(0xFF3AD29F),
          unselectedItemColor: const Color(0xFF5A8A8A),
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
          items: const [
            BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.search_outlined),
              activeIcon: Icon(Icons.search),
              label: 'Search',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.add_circle_outline),
              activeIcon: Icon(Icons.add_circle),
              label: 'List Item',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.chat_bubble_outline),
              activeIcon: Icon(Icons.chat_bubble),
              label: 'Messages',
            ),
            BottomNavigationBarItem(
              icon: Icon(Icons.person_outline),
              activeIcon: Icon(Icons.person),
              label: 'Profile',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentPage() {
    switch (_currentIndex) {
      case 0:
        return _buildHomePage();
      case 1:
        return _buildSearchPage();
      case 2:
        return _buildListItemPage();
      case 3:
        return _buildMessagesPage();
      case 4:
        return _buildProfilePage();
      default:
        return _buildHomePage();
    }
  }

  Widget _buildHomePage() {
    final user = AuthService.currentUser;

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome section (conditional)
          if (_showWelcome) ...[
            AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: const Color(0xFFA8C5B8), // Same sage green
                borderRadius: BorderRadius.circular(24),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isFirstTimeUser ? 'Welcome!' : 'Welcome back!',
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D5A5A),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    user?.displayName ?? user?.email ?? 'User',
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF2D5A5A),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _isFirstTimeUser
                        ? 'Welcome to EvenOut! Ready to start exchanging goods and connecting with your community?'
                        : 'Ready to exchange goods and connect with your community?',
                    style: const TextStyle(
                      fontSize: 16,
                      color: Color(0xFF5A8A8A),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 32),
          ],

          // Recent Activity or Featured Items
          const Text(
            'Recent Activity',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2D5A5A),
            ),
          ),

          const SizedBox(height: 16),

          // Placeholder content for home page
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.inbox_outlined,
                      size: 64,
                      color: Color(0xFF5A8A8A),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'No recent activity',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2D5A5A),
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Start by listing an item or browsing what others have to offer!',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF5A8A8A),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchPage() {
    return const Padding(
      padding: EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search,
              size: 64,
              color: Color(0xFF5A8A8A),
            ),
            SizedBox(height: 16),
            Text(
              'Search',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D5A5A),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Search functionality coming soon!',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF5A8A8A),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListItemPage() {
    // Navigate to ListItemScreen when this tab is selected
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).push(
        MaterialPageRoute(builder: (_) => const ListItemScreen()),
      ).then((_) {
        // Return to home tab after coming back from ListItemScreen
        setState(() {
          _currentIndex = 0;
        });
      });
    });

    // Show a loading indicator while navigating
    return const Center(
      child: CircularProgressIndicator(
        color: Color(0xFF3AD29F),
      ),
    );
  }

  Widget _buildMessagesPage() {
    return const Padding(
      padding: EdgeInsets.all(24),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble,
              size: 64,
              color: Color(0xFFE6C068),
            ),
            SizedBox(height: 16),
            Text(
              'Messages',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2D5A5A),
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Messages functionality coming soon!',
              style: TextStyle(
                fontSize: 16,
                color: Color(0xFF5A8A8A),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfilePage() {
    final user = AuthService.currentUser;

    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Profile header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: const Color(0xFFA8C5B8),
              borderRadius: BorderRadius.circular(24),
            ),
            child: Column(
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundColor: const Color(0xFF2D5A5A),
                  backgroundImage: user?.photoURL != null
                      ? NetworkImage(user!.photoURL!)
                      : null,
                  child: user?.photoURL == null
                      ? const Icon(
                          Icons.person,
                          size: 40,
                          color: Colors.white,
                        )
                      : null,
                ),
                const SizedBox(height: 16),
                Text(
                  user?.displayName ?? 'User',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2D5A5A),
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user?.email ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF5A8A8A),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Profile options
          Expanded(
            child: Column(
              children: [
                _buildProfileOption(
                  icon: Icons.edit_outlined,
                  title: 'Edit Profile',
                  onTap: () {
                    // TODO: Navigate to edit profile
                  },
                ),
                _buildProfileOption(
                  icon: Icons.settings_outlined,
                  title: 'Settings',
                  onTap: () {
                    // TODO: Navigate to settings
                  },
                ),
                _buildProfileOption(
                  icon: Icons.help_outline,
                  title: 'Help & Support',
                  onTap: () {
                    // TODO: Navigate to help
                  },
                ),
                _buildProfileOption(
                  icon: Icons.logout,
                  title: 'Sign Out',
                  onTap: () => _handleSignOut(context),
                  isDestructive: true,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isDestructive ? const Color(0xFFE74C3C) : const Color(0xFF5A8A8A),
                  size: 24,
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: isDestructive ? const Color(0xFFE74C3C) : const Color(0xFF2D5A5A),
                  ),
                ),
                const Spacer(),
                if (!isDestructive)
                  const Icon(
                    Icons.arrow_forward_ios,
                    color: Color(0xFF5A8A8A),
                    size: 16,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}